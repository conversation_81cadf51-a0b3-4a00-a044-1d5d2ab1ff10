"""
API views for the hoplogic application.

This module contains Django Rest Framework API views for chat and recipe functionality.
"""

import logging

from django.shortcuts import get_object_or_404
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from ..agents.master_brewer import MasterBrewerAgent
from ..models import Recipe
from ..serializers import ChatMessageSerializer, RecipeSerializer

logger = logging.getLogger(__name__)

# Global agent instance to maintain memory across requests
_master_brewer_agent = None


def get_master_brewer_agent():
    """Get or create the master brewer agent singleton."""
    global _master_brewer_agent
    if _master_brewer_agent is None:
        logger.info("Creating new master brewer agent instance")
        _master_brewer_agent = MasterBrewerAgent.create()
    else:
        logger.debug("Reusing existing master brewer agent instance")
    return _master_brewer_agent


class RecipeDetailAPIView(generics.RetrieveAPIView):
    """API endpoint for retrieving recipe details."""

    serializer_class = RecipeSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        """Return recipe only if owned by the current user."""
        recipe = get_object_or_404(Recipe, pk=self.kwargs["pk"], user=self.request.user)
        return recipe


@method_decorator(csrf_exempt, name="dispatch")
class ChatStreamAPIView(APIView):
    """API endpoint for chat responses from the master brewer agent."""

    permission_classes = [IsAuthenticated]

    def post(self, request, recipe_pk):
        """Send a message to the agent and return the response."""
        # Get the recipe and verify ownership
        recipe = get_object_or_404(Recipe, pk=recipe_pk, user=request.user)

        # Validate the message
        serializer = ChatMessageSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        message = serializer.validated_data["message"]

        logger.info(
            f"Processing chat message for recipe {recipe_pk}: {message[:50]}..."
        )

        try:
            # Get the shared agent instance (maintains memory across requests)
            agent = get_master_brewer_agent()

            # Collect all events
            events = []
            for event in agent.run(message, recipe):
                event_data = {
                    "type": "agent_event",
                    "event_type": event.__class__.__name__,
                    "content": event.display(),
                }

                # Add additional data for specific event types
                if hasattr(event, "tool_calls"):
                    event_data["tool_calls"] = [
                        {"id": call.id, "tool_name": call.tool_name, "args": call.args}
                        for call in event.tool_calls
                    ]
                elif hasattr(event, "tool_outputs"):
                    event_data["tool_outputs"] = [
                        {"id": output.id, "output": output.output}
                        for output in event.tool_outputs
                    ]

                events.append(event_data)

            return Response(
                {"status": "success", "events": events}, status=status.HTTP_200_OK
            )

        except Exception as e:
            # print a detailed error message to the log
            logger.exception("Error processing chat message", exc_info=True)
            return Response(
                {"status": "error", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class RecipeRefreshAPIView(generics.RetrieveAPIView):
    """API endpoint for refreshing recipe data after agent modifications."""

    serializer_class = RecipeSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        """Return fresh recipe data from database."""
        recipe = get_object_or_404(
            Recipe.objects.prefetch_related(
                "fermentableinclusion_set__fermentable",
                "yeastinclusion_set__yeast",
                "mashstep_set",
                "fermentationphase_set",
            ).select_related("water_profile"),
            pk=self.kwargs["pk"],
            user=self.request.user,
        )
        return recipe
